'use client';

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@fitness-rewards/db';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import {
  Loader2,
  CheckCircle,
  XCircle,
  Settings,
  ExternalLink,
  AlertTriangle,
  Users,
} from 'lucide-react';

interface IntegrationsTabProps {
  onReviewEvents: () => void;
}

interface IntegrationCardProps {
  provider: string;
  displayName: string;
  description: string;
  isConfigured: boolean;
  isActive: boolean;
  lastTestStatus?: 'success' | 'failed';
  lastTestAt?: number;
  onConfigure: () => void;
}

function IntegrationCard({
  provider,
  displayName,
  description,
  isConfigured,
  isActive,
  lastTestStatus,
  lastTestAt,
  onConfigure,
}: IntegrationCardProps) {
  const getStatusBadge = () => {
    if (!isConfigured) {
      return <Badge variant="secondary">Not Connected</Badge>;
    }
    if (!isActive) {
      return <Badge variant="destructive">Inactive</Badge>;
    }
    if (lastTestStatus === 'success') {
      return (
        <Badge variant="default" className="bg-green-500">
          <CheckCircle className="mr-1 h-3 w-3" />
          Connected
        </Badge>
      );
    }
    if (lastTestStatus === 'failed') {
      return (
        <Badge variant="destructive">
          <XCircle className="mr-1 h-3 w-3" />
          Error
        </Badge>
      );
    }
    return <Badge variant="outline">Unknown</Badge>;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {displayName}
              {getStatusBadge()}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Button onClick={onConfigure} variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Configure
          </Button>
        </div>
      </CardHeader>
      {isConfigured && (
        <CardContent>
          <div className="text-muted-foreground text-sm">
            {lastTestAt && (
              <p>Last tested: {new Date(lastTestAt).toLocaleString()}</p>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
}

interface ConfigurationModalProps {
  provider: string;
  displayName: string;
  isOpen: boolean;
  onClose: () => void;
}

function ConfigurationModal({
  provider,
  displayName,
  isOpen,
  onClose,
}: ConfigurationModalProps) {
  const [apiKey, setApiKey] = useState('');
  const [webhookSecret, setWebhookSecret] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const configureIntegration = useMutation(
    api.functions.integrations.configureIntegration
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!apiKey.trim()) {
      toast.error('API Key is required');
      return;
    }

    setIsLoading(true);
    try {
      const result = await configureIntegration({
        provider,
        apiKey: apiKey.trim(),
        webhookSecret: webhookSecret.trim() || undefined,
      });

      if (result.testResult.success) {
        toast.success(`${displayName} integration configured successfully!`);
        onClose();
        setApiKey('');
        setWebhookSecret('');
      } else {
        toast.error(`Configuration failed: ${result.testResult.error}`);
      }
    } catch (error) {
      toast.error('Failed to configure integration');
      console.error('Configuration error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getInstructions = () => {
    switch (provider) {
      case 'marianaTek':
        return (
          <div className="text-muted-foreground space-y-2 text-sm">
            <p>To configure Mariana-Tek integration:</p>
            <ol className="ml-4 list-inside list-decimal space-y-1">
              <li>Contact <EMAIL> to request API access</li>
              <li>Agree to the API Terms of Service</li>
              <li>Obtain your API key from the Mariana-Tek admin panel</li>
              <li>Enter the API key below and test the connection</li>
            </ol>
            <p className="mt-2">
              <ExternalLink className="mr-1 inline h-3 w-3" />
              <a
                href="https://docs.marianatek.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                View Mariana-Tek API Documentation
              </a>
            </p>
          </div>
        );
      default:
        return (
          <p className="text-muted-foreground text-sm">
            Enter your API credentials below.
          </p>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Configure {displayName} Integration</DialogTitle>
          <DialogDescription>
            Set up automated activity logging from {displayName}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {getInstructions()}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key *</Label>
              <Input
                id="apiKey"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key"
                required
              />
            </div>

            {provider === 'marianaTek' && (
              <div className="space-y-2">
                <Label htmlFor="webhookSecret">Webhook Secret (Optional)</Label>
                <Input
                  id="webhookSecret"
                  type="password"
                  value={webhookSecret}
                  onChange={(e) => setWebhookSecret(e.target.value)}
                  placeholder="Enter webhook secret if available"
                />
                <p className="text-muted-foreground text-xs">
                  Note: Mariana-Tek currently uses polling instead of webhooks
                  for check-in events.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Test & Save
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function IntegrationsTab({
  onReviewEvents,
}: IntegrationsTabProps) {
  const [configureProvider, setConfigureProvider] = useState<string | null>(
    null
  );

  const integrationStatus = useQuery(
    api.functions.integrations.getIntegrationStatus
  );
  const unmatchedEventsSummary = useQuery(
    api.functions.unmatchedEvents.getUnmatchedEventsSummary
  );

  const availableIntegrations = [
    {
      provider: 'marianaTek',
      displayName: 'Mariana-Tek',
      description:
        'Automatically log class check-ins from your Mariana-Tek booking system.',
    },
    {
      provider: 'mindBody',
      displayName: 'MindBody',
      description:
        'Automatically log class check-ins from your MindBody booking system. (Coming Soon)',
    },
  ];

  const getIntegrationData = (provider: string) => {
    const credential = integrationStatus?.credentials.find(
      (c) => c.provider === provider
    );
    const syncStatus = integrationStatus?.syncStatuses.find(
      (s) => s.provider === provider
    );

    return {
      isConfigured: !!credential,
      isActive: credential?.isActive || false,
      lastTestStatus: credential?.lastTestStatus,
      lastTestAt: credential?.lastTestAt,
      syncStatus,
    };
  };

  if (!integrationStatus) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Integrations</h2>
        <p className="text-muted-foreground">
          Connect your fitness management systems to automatically log member
          activities.
        </p>
      </div>

      <div className="grid gap-4">
        {availableIntegrations.map((integration) => {
          const data = getIntegrationData(integration.provider);

          return (
            <IntegrationCard
              key={integration.provider}
              provider={integration.provider}
              displayName={integration.displayName}
              description={integration.description}
              isConfigured={data.isConfigured}
              isActive={data.isActive}
              lastTestStatus={data.lastTestStatus}
              lastTestAt={data.lastTestAt}
              onConfigure={() => setConfigureProvider(integration.provider)}
            />
          );
        })}
      </div>

      {/* Sync Status Summary */}
      {integrationStatus.syncStatuses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Sync Status</CardTitle>
            <CardDescription>
              Recent activity synchronization statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {integrationStatus.syncStatuses.map((status) => (
                <div key={status.provider} className="space-y-2">
                  <h4 className="font-medium">{status.provider}</h4>
                  <div className="text-muted-foreground space-y-1 text-sm">
                    <p>
                      Last sync:{' '}
                      {status.lastSyncAt
                        ? new Date(status.lastSyncAt).toLocaleString()
                        : 'Never'}
                    </p>
                    <p>Events processed: {status.eventsProcessed}</p>
                    <p>Successfully matched: {status.eventsMatched}</p>
                    <p>Unmatched: {status.eventsUnmatched}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Unmatched Events Alert */}
      {unmatchedEventsSummary && unmatchedEventsSummary.pending > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              Unmatched Events Require Attention
            </CardTitle>
            <CardDescription className="text-orange-700">
              {unmatchedEventsSummary.pending} events from external systems
              couldn't be automatically matched to users.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-sm text-orange-700">
                <p>
                  These events need manual review to link them to the correct
                  users.
                </p>
                <p className="mt-1">
                  Total unmatched: {unmatchedEventsSummary.pending} | Resolved:{' '}
                  {unmatchedEventsSummary.resolved} | Ignored:{' '}
                  {unmatchedEventsSummary.ignored}
                </p>
              </div>
              <Button
                variant="outline"
                className="border-orange-300 text-orange-800 hover:bg-orange-100"
                onClick={onReviewEvents}
              >
                <Users className="mr-2 h-4 w-4" />
                Review Events
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Configuration Modals */}
      {availableIntegrations.map((integration) => (
        <ConfigurationModal
          key={integration.provider}
          provider={integration.provider}
          displayName={integration.displayName}
          isOpen={configureProvider === integration.provider}
          onClose={() => setConfigureProvider(null)}
        />
      ))}
    </div>
  );
}
