import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@db';
import { Doc, Id } from '@db/types';
import toast from 'react-hot-toast';
import { ConfirmationDialog } from '../ui/ConfirmationDialog';
import { LinkUserModal } from './LinkUserModal';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, UserPlus, Trash2, ShieldOff } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { WithLoading } from '../utils/WithLoading';

// We will create this component later
// import { LinkUserModal } from './LinkUserModal';

type UnmatchedEvent = Doc<'unmatchedEvents'>;

const UnmatchedEventsTab: React.FC = () => {
  const [statusFilter, setStatusFilter] = useState<
    'pending' | 'resolved' | 'ignored'
  >('pending');
  const [selectedEvents, setSelectedEvents] = useState<Id<'unmatchedEvents'>[]>(
    []
  );
  const [eventToIgnore, setEventToIgnore] = useState<UnmatchedEvent | null>(
    null
  );
  const [isBulkIgnoring, setIsBulkIgnoring] = useState(false);
  const [eventToLink, setEventToLink] = useState<UnmatchedEvent | null>(null);

  const events = useQuery(api.functions.unmatchedEvents.getUnmatchedEvents, {
    status: statusFilter,
    limit: 100,
  });
  const bulkIgnore = useMutation(
    api.functions.unmatchedEvents.bulkIgnoreUnmatchedEvents
  );
  const ignoreEvent = useMutation(
    api.functions.unmatchedEvents.ignoreUnmatchedEvent
  );

  const isLoading = events === undefined;

  const handleToggleAll = (checked: boolean) => {
    if (checked) {
      setSelectedEvents(events?.map((e) => e._id) || []);
    } else {
      setSelectedEvents([]);
    }
  };

  const handleToggleOne = (
    eventId: Id<'unmatchedEvents'>,
    checked: boolean
  ) => {
    if (checked) {
      setSelectedEvents((prev) => [...prev, eventId]);
    } else {
      setSelectedEvents((prev) => prev.filter((id) => id !== eventId));
    }
  };

  const executeBulkIgnore = async () => {
    if (selectedEvents.length === 0) return;
    setIsBulkIgnoring(true);
    await toast.promise(bulkIgnore({ eventIds: selectedEvents }), {
      loading: 'Ignoring selected events...',
      success: (res) => {
        setSelectedEvents([]);
        return `Successfully ignored ${res.processed} events.`;
      },
      error: 'Failed to ignore events.',
    });
    setIsBulkIgnoring(false);
  };

  const executeIgnore = async () => {
    if (!eventToIgnore) return;
    await toast.promise(ignoreEvent({ eventId: eventToIgnore._id }), {
      loading: 'Ignoring event...',
      success: 'Event ignored.',
      error: 'Failed to ignore event.',
    });
    setEventToIgnore(null);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Unmatched Events</h2>
          <p className="text-muted-foreground">
            Review and resolve events that could not be automatically matched.
          </p>
        </div>
        <div>
          {selectedEvents.length > 0 && (
            <Button
              variant="outline"
              onClick={() => setIsBulkIgnoring(true)}
              disabled={isLoading}
            >
              <ShieldOff className="mr-2 h-4 w-4" />
              Ignore Selected ({selectedEvents.length})
            </Button>
          )}
        </div>
      </div>

      <div className="flex space-x-2 border-b">
        {(['pending', 'resolved', 'ignored'] as const).map((status) => (
          <Button
            key={status}
            variant={statusFilter === status ? 'secondary' : 'ghost'}
            onClick={() => {
              setStatusFilter(status);
              setSelectedEvents([]);
            }}
            className="capitalize"
          >
            {status}
          </Button>
        ))}
      </div>

      <Card>
        <CardContent>
          <WithLoading isPending={isLoading}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Checkbox
                      checked={
                        events?.length > 0 &&
                        selectedEvents.length === events?.length
                      }
                      onCheckedChange={handleToggleAll}
                      disabled={statusFilter !== 'pending'}
                    />
                  </TableHead>
                  <TableHead>External User</TableHead>
                  <TableHead>Provider</TableHead>
                  <TableHead>Event Type</TableHead>
                  <TableHead>Received At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {events?.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No {statusFilter} events found.
                    </TableCell>
                  </TableRow>
                ) : (
                  events?.map((event) => (
                    <TableRow key={event._id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedEvents.includes(event._id)}
                          onCheckedChange={(checked) =>
                            handleToggleOne(event._id, checked as boolean)
                          }
                          disabled={statusFilter !== 'pending'}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {event.externalUserName || 'N/A'}
                        </div>
                        <div className="text-muted-foreground text-sm">
                          {event.externalUserEmail}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{event.provider}</Badge>
                      </TableCell>
                      <TableCell>{event.eventType}</TableCell>
                      <TableCell>
                        {new Date(event.receivedAt).toLocaleString()}
                      </TableCell>
                      <TableCell className="text-right">
                        {statusFilter === 'pending' && (
                          <div className="space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEventToLink(event)}
                            >
                              <UserPlus className="mr-2 h-4 w-4" />
                              Link User
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setEventToIgnore(event)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </WithLoading>
        </CardContent>
      </Card>

      {eventToIgnore && (
        <ConfirmationDialog
          isOpen={!!eventToIgnore}
          onClose={() => setEventToIgnore(null)}
          onConfirm={executeIgnore}
          title="Ignore Event"
          description={`Are you sure you want to ignore this event from "${
            eventToIgnore.externalUserName || eventToIgnore.externalUserEmail
          }"? This is reversible, but the event will be hidden.`}
          confirmButtonText="Yes, Ignore"
          variant="danger"
        />
      )}

      {isBulkIgnoring && (
        <ConfirmationDialog
          isOpen={isBulkIgnoring}
          onClose={() => setIsBulkIgnoring(false)}
          onConfirm={executeBulkIgnore}
          title="Bulk Ignore Events"
          description={`Are you sure you want to ignore ${selectedEvents.length} selected events?`}
          confirmButtonText="Yes, Ignore All"
          variant="danger"
        />
      )}

      {eventToLink && (
        <LinkUserModal
          event={eventToLink}
          isOpen={!!eventToLink}
          onClose={() => setEventToLink(null)}
        />
      )}
    </div>
  );
};

export default UnmatchedEventsTab;
